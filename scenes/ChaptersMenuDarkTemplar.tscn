[gd_scene load_steps=12 format=3 uid="uid://dark_templar_chapters"]

[ext_resource type="Script" path="res://scripts/ChaptersMenuVerticalSafe.gd" id="1_script"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_background"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_main_panel"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel1.png" id="4_chapter_list_panel"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel2.png" id="5_content_panel"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Mask Img.png" id="6_image_mask"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png" id="7_button_normal"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png" id="8_button_hover"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Button Normal.png" id="9_chapter_button_normal"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Button Hover.png" id="10_chapter_button_hover"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Title Description.png" id="11_title_panel"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Button Close Panel/Button Close Normal.png" id="12_close_button"]

[node name="ChaptersMenuDarkTemplar" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 40.0
offset_right = -20.0
offset_bottom = -40.0
texture = ExtResource("3_main_panel")
patch_margin_left = 50
patch_margin_top = 50
patch_margin_right = 50
patch_margin_bottom = 50

[node name="TitlePanel" type="NinePatchRect" parent="MainPanel"]
layout_mode = 1
anchors_preset = 2
anchor_top = 0.0
anchor_bottom = 0.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 80.0
texture = ExtResource("11_title_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="TitleLabel" type="Label" parent="MainPanel/TitlePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 10.0
offset_right = -20.0
offset_bottom = -10.0
text = "VÝBER KAPITOLY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CloseButton" type="TextureButton" parent="MainPanel"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -60.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 60.0
texture_normal = ExtResource("12_close_button")
stretch_mode = 4

[node name="CloseLabel" type="Label" parent="MainPanel/CloseButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "✕"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ContentContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 100.0
offset_right = -30.0
offset_bottom = -30.0

[node name="ChapterListPanel" type="NinePatchRect" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 350)
size_flags_vertical = 0
texture = ExtResource("4_chapter_list_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="ChapterListContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/ChapterListPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="ChapterListTitle" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer"]
layout_mode = 2
text = "KAPITOLY"
horizontal_alignment = 1

[node name="ChapterListSpacer" type="Control" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 10)

[node name="ChapterButtons" type="VBoxContainer" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Chapter1Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter1Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. I - Búrlivá cesta"
vertical_alignment = 1

[node name="Chapter2Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter2Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. II - Brána zámku"
vertical_alignment = 1

[node name="Chapter3Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter3Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. III - Pátranie"
vertical_alignment = 1

[node name="Chapter4Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter4Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. IV - Tajné krídlo"
vertical_alignment = 1

[node name="Chapter5Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter5Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. V - Temné tajomstvá"
vertical_alignment = 1

[node name="Chapter6Button" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="Chapter6Label" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Kap. VI - Konfrontácia"
vertical_alignment = 1

[node name="EpilogueButton" type="TextureButton" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
texture_normal = ExtResource("9_chapter_button_normal")
texture_hover = ExtResource("10_chapter_button_hover")
stretch_mode = 0

[node name="EpilogueLabel" type="Label" parent="MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "▶ Epilóg"
vertical_alignment = 1

[node name="ContentSpacer" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ChapterDetailPanel" type="NinePatchRect" parent="MainPanel/ContentContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("5_content_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="ChapterDetailContent" type="HBoxContainer" parent="MainPanel/ContentContainer/ChapterDetailPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0

[node name="ImageContainer" type="CenterContainer" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent"]
layout_mode = 2
size_flags_horizontal = 0
custom_minimum_size = Vector2(200, 0)

[node name="ImageFrame" type="NinePatchRect" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/ImageContainer"]
layout_mode = 2
custom_minimum_size = Vector2(180, 180)
texture = ExtResource("6_image_mask")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="ChapterImage" type="TextureRect" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/ImageContainer/ImageFrame"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0
stretch_mode = 6

[node name="ContentSpacer2" type="Control" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent"]
layout_mode = 2
custom_minimum_size = Vector2(20, 0)

[node name="InfoContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ChapterTitle" type="Label" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
text = "KAPITOLA I"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 10)

[node name="ChapterSubtitle" type="Label" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
text = "BÚRLIVÁ CESTA"
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ChapterDescription" type="RichTextLabel" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "Popis kapitoly bude tu..."
fit_content = true

[node name="Spacer3" type="Control" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ProgressContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2

[node name="ProgressLabel" type="Label" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer"]
layout_mode = 2
text = "Postup:"
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)
max_value = 100.0
step = 1.0
value = 0.0
show_percentage = false

[node name="Spacer4" type="Control" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ButtonContainer" type="HBoxContainer" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer"]
layout_mode = 2

[node name="PlayButton" type="TextureButton" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("7_button_normal")
texture_hover = ExtResource("8_button_hover")
stretch_mode = 0

[node name="PlayLabel" type="Label" parent="MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "HRAŤ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BottomSpacer" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="BottomButtonsContainer" type="HBoxContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="BackButton" type="TextureButton" parent="MainPanel/ContentContainer/BottomButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("7_button_normal")
texture_hover = ExtResource("8_button_hover")
stretch_mode = 0

[node name="BackLabel" type="Label" parent="MainPanel/ContentContainer/BottomButtonsContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonSpacer" type="Control" parent="MainPanel/ContentContainer/BottomButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 3
