extends Control
class_name ChaptersMenuMobile

# UI References
@onready var chapter_title: Label = $SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterTitle
@onready var chapter_subtitle: Label = $SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterSubtitle
@onready var chapter_description: RichTextLabel = $SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterDescription
@onready var chapter_image: TextureRect = $SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer/ChapterImageMask/ChapterImage

# Buttons
@onready var close_button: TextureButton = $SafeArea/MainContainer/TitleContainer/CloseButton
@onready var back_button: TextureButton = $SafeArea/MainContainer/BottomButtonsContainer/BackButton
@onready var play_button: TextureButton = $SafeArea/MainContainer/BottomButtonsContainer/PlayButton

# Chapter buttons
@onready var chapter_buttons: Array[TextureButton] = [
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter1Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter2Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter3Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter4Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter5Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter6Button,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/EpilogueButton
]

@onready var chapter_labels: Array[Label] = [
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter1Button/Chapter1Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter2Button/Chapter2Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter3Button/Chapter3Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter4Button/Chapter4Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter5Button/Chapter5Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter6Button/Chapter6Label,
	$SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/EpilogueButton/EpilogueLabel
]

# Chapter Selection settings
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "Epilóg"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "[center][color=#D4AF37][b]Marec 1894[/b][/color][/center]\n\nBúrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Navigačná šifra a Krvavý nápis[/i][/color]",
		"unlocked": true,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Interior_view_of_ornate_Victorian_horse-drawn_car_514b5c75-3caa-4a98-9002-82c1c9326dc1_0.png"
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "[center][color=#D4AF37][b]Vstup do zámku[/b][/color][/center]\n\nVstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Krvavý nápis a Skúška Rádu[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_medieval_castle_gate_with_weathered_iron__ff9848cd-5fdb-41fe-b04c-027d685a6e1e_0.png"
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "[center][color=#D4AF37][b]Hľadanie stôp[/b][/color][/center]\n\nVstúpte do Van Helsingovho zámku a nájdite stopy po jeho zmiznutí. Preskúmajte veľkú sálu a rozlúštite záhadné správy.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Obrátená správa a Jednoduchý výpočet[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "[center][color=#D4AF37][b]Skryté chodby[/b][/color][/center]\n\nObjavte tajné krídlo zámku a jeho temné tajomstvá. Prekonajte test pamäte a vyriešte vampírsku aritmetiku.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Test pamäte a Vampírska aritmetika[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Ancient_stone_castle_corridor_with_damp_moss-cove_d1335a08-f001-429f-928d-da3a0e7319b3_2.png"
	},
	{
		"number": 5,
		"title": "TEMNÉ TAJOMSTVÁ",
		"description": "[center][color=#D4AF37][b]Hlbiny zámku[/b][/color][/center]\n\nSestúpte do najtemnejších častí zámku a odhaľte jeho najväčšie tajomstvá. Čelite starým kliatbam a záhadám.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Kamenné schodisko a Staroveká kliатba[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Ancient_stone_staircase_descending_into_darkness_b8c7e8b1-b8a4-4b8e-9c2d-f3e1a7b9c4d6_0.png"
	},
	{
		"number": 6,
		"title": "KONFRONTÁCIA",
		"description": "[center][color=#D4AF37][b]Finálny súboj[/b][/color][/center]\n\nKonečná konfrontácia s temnotou. Vyriešte posledné hlavolamy a čelte svojmu osudu v srdci Van Helsingovho zámku.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Tri sestry a Rytmus rituálu[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_stone_sarcophagus_cracking_under_tremendo_eda4a822-6964-41b6-87d0-6091ff5eeebd_2.png"
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "[center][color=#D4AF37][b]Koniec príbehu[/b][/color][/center]\n\nZáverečná kapitola vášho dobrodružstva. Uvidíte dôsledky svojich činov a uzavriete príbeh Van Helsingovho dedičstva.\n\n[color=#E0E0E0][i]Záverečná kapitola bez hlavolamov[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Magnificent_sunrise_breaking_through_clearing_sto_ae111b3c-e624-4fb0-b07d-b18319925104_1.png"
	}
]

func _ready():
	"""Inicializácia menu"""
	load_chapter_progress()
	connect_signals()
	update_chapter_display()
	update_chapter_list()

func connect_signals():
	"""Pripojenie signálov"""
	close_button.pressed.connect(_on_back_pressed)
	back_button.pressed.connect(_on_back_pressed)
	play_button.pressed.connect(_on_play_pressed)
	
	# Pripojenie signálov pre tlačidlá kapitol
	for i in range(chapter_buttons.size()):
		chapter_buttons[i].pressed.connect(_on_chapter_selected.bind(i))

func load_chapter_progress():
	"""Načítanie postupu z GameManager"""
	for i in range(chapter_data.size()):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func update_chapter_list():
	"""Aktualizácia zoznamu kapitol"""
	for i in range(chapter_labels.size()):
		var chapter = chapter_data[i]
		var label = chapter_labels[i]
		
		# Zvýraznenie aktuálnej kapitoly
		if i == current_chapter_index:
			label.modulate = Color("#D4AF37")  # Zlatá
			label.text = "► " + get_chapter_display_name(i)
		else:
			# Farba podľa stavu
			if chapter.completed:
				label.modulate = Color(0.2, 0.8, 0.2)  # Zelená
				label.text = "✓ " + get_chapter_display_name(i)
			elif chapter.unlocked:
				label.modulate = Color("#F5F5DC")  # Krémová
				label.text = "▶ " + get_chapter_display_name(i)
			else:
				label.modulate = Color(0.5, 0.5, 0.5)  # Sivá
				label.text = "🔒 " + get_chapter_display_name(i)

func get_chapter_display_name(index: int) -> String:
	"""Získanie názvu kapitoly pre zobrazenie"""
	var chapter = chapter_data[index]
	if index == chapter_data.size() - 1:  # Epilóg
		return "Epilóg"
	else:
		return roman_numerals[index] + ". " + chapter.title.split(" ")[0] + " " + chapter.title.split(" ")[1]

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	chapter_title.text = title_text
	
	# Update content
	chapter_subtitle.text = chapter.title
	chapter_description.text = chapter.description
	
	# Update preview image
	if ResourceLoader.exists(chapter.image_path):
		chapter_image.texture = load(chapter.image_path)
	else:
		chapter_image.texture = null
	
	# Update play button state
	play_button.disabled = not chapter.unlocked
	if chapter.unlocked:
		play_button.modulate = Color.WHITE
	else:
		play_button.modulate = Color(0.5, 0.5, 0.5)

func _on_chapter_selected(index: int):
	"""Výber kapitoly zo zoznamu"""
	current_chapter_index = index
	if AudioManager: AudioManager.play_menu_button_sound()
	update_chapter_display()
	update_chapter_list()

func _on_play_pressed():
	"""Spustenie kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	if chapter.unlocked:
		if AudioManager: AudioManager.play_menu_button_sound()
		var scene_path = "res://scenes/Chapter" + str(chapter.number) + ".tscn"
		get_tree().change_scene_to_file(scene_path)
	else:
		# Zvuk chyby pre uzamknuté kapitoly
		if AudioManager: AudioManager.play_puzzle_error_sound()

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	if AudioManager: AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Klávesové skratky"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
